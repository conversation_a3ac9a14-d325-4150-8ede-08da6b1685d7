"""
mitmproxy script to log detailed request/response data
Captures full request bodies, response bodies, headers, and timing
"""

import json
import time
from datetime import datetime
from mitmproxy import http
import os

# Log file path
LOG_DIR = "/home/<USER>/.mitmproxy"
LOG_FILE = os.path.join(LOG_DIR, "detailed_logs.jsonl")

def request(flow: http.HTTPFlow) -> None:
    """Called when a request is received"""
    flow.request.timestamp_start = time.time()

def response(flow: http.HTTPFlow) -> None:
    """Called when a response is received"""
    timestamp_end = time.time()
    duration_ms = int((timestamp_end - flow.request.timestamp_start) * 1000)
    
    # Parse request body
    request_body = ""
    request_json = None
    if flow.request.content:
        try:
            request_body = flow.request.content.decode('utf-8', errors='ignore')
            if flow.request.headers.get('content-type', '').startswith('application/json'):
                request_json = json.loads(request_body)
        except:
            request_body = f"<binary data: {len(flow.request.content)} bytes>"
    
    # Parse response body
    response_body = ""
    response_json = None
    if flow.response.content:
        try:
            response_body = flow.response.content.decode('utf-8', errors='ignore')
            if flow.response.headers.get('content-type', '').startswith('application/json'):
                response_json = json.loads(response_body)
        except:
            response_body = f"<binary data: {len(flow.response.content)} bytes>"
    
    # Create detailed log entry
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "duration_ms": duration_ms,
        "request": {
            "method": flow.request.method,
            "url": flow.request.pretty_url,
            "path": flow.request.path,
            "query": dict(flow.request.query),
            "headers": dict(flow.request.headers),
            "body_raw": request_body[:2000] if len(request_body) <= 2000 else request_body[:2000] + "...",
            "body_json": request_json,
            "content_length": len(flow.request.content) if flow.request.content else 0
        },
        "response": {
            "status_code": flow.response.status_code,
            "reason": flow.response.reason,
            "headers": dict(flow.response.headers),
            "body_raw": response_body[:2000] if len(response_body) <= 2000 else response_body[:2000] + "...",
            "body_json": response_json,
            "content_length": len(flow.response.content) if flow.response.content else 0
        },
        "client": {
            "ip": flow.client_conn.address[0] if flow.client_conn.address else "unknown",
            "port": flow.client_conn.address[1] if flow.client_conn.address else 0
        }
    }
    
    # Write to log file
    try:
        os.makedirs(LOG_DIR, exist_ok=True)
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    except Exception as e:
        print(f"Error writing log: {e}")

def load():
    """Called when the script is loaded"""
    print("mitmproxy detailed logging script loaded")
    print(f"Logs will be written to: {LOG_FILE}")
