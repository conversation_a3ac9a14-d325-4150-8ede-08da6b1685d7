<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eko API Request/Response Logs</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #1a1a1a; color: #e0e0e0; }
        .header { background: #2d2d2d; padding: 1rem; border-bottom: 2px solid #4a9eff; }
        .header h1 { color: #4a9eff; font-size: 1.5rem; }
        .controls { background: #2a2a2a; padding: 1rem; display: flex; gap: 1rem; align-items: center; }
        .btn { background: #4a9eff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #357abd; }
        .logs-container { padding: 1rem; max-height: calc(100vh - 140px); overflow-y: auto; }
        .log-entry { background: #2d2d2d; margin-bottom: 1rem; border-radius: 8px; border-left: 4px solid #4a9eff; }
        .log-header { padding: 1rem; background: #333; border-radius: 8px 8px 0 0; cursor: pointer; }
        .log-details { padding: 1rem; display: none; border-top: 1px solid #444; }
        .method { padding: 0.2rem 0.5rem; border-radius: 4px; color: white; font-weight: bold; margin-right: 0.5rem; }
        .GET { background: #28a745; }
        .POST { background: #007bff; }
        .PUT { background: #ffc107; color: #000; }
        .DELETE { background: #dc3545; }
        .status-200 { color: #28a745; }
        .status-400, .status-404 { color: #ffc107; }
        .status-500 { color: #dc3545; }
        .timestamp { color: #888; font-size: 0.9rem; }
        .url { color: #4a9eff; font-weight: bold; }
        .headers { background: #1a1a1a; padding: 0.5rem; border-radius: 4px; margin: 0.5rem 0; }
        .header-item { margin: 0.2rem 0; font-family: monospace; font-size: 0.9rem; }
        .key { color: #4a9eff; }
        .value { color: #e0e0e0; }
        .duration { color: #ffc107; font-weight: bold; }
        .auto-refresh { margin-left: auto; display: flex; align-items: center; gap: 0.5rem; }
        .loading { text-align: center; padding: 2rem; color: #888; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Eko API Request/Response Logs</h1>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="loadLogs()">🔄 Refresh</button>
        <button class="btn" onclick="clearLogs()">🗑️ Clear</button>
        <div class="auto-refresh">
            <label>
                <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()"> Auto-refresh (5s)
            </label>
        </div>
    </div>
    
    <div class="logs-container" id="logsContainer">
        <div class="loading">Loading logs...</div>
    </div>

    <script>
        let autoRefreshInterval;
        
        function loadLogs() {
            fetch('/api/logs')
                .then(response => response.text())
                .then(data => {
                    const logs = data.trim().split('\n').filter(line => line.trim())
                        .map(line => {
                            try {
                                return JSON.parse(line);
                            } catch (e) {
                                return null;
                            }
                        })
                        .filter(log => log)
                        .reverse()
                        .slice(0, 100); // Show last 100 requests
                    
                    displayLogs(logs);
                })
                .catch(error => {
                    document.getElementById('logsContainer').innerHTML = 
                        '<div class="loading">Error loading logs: ' + error.message + '</div>';
                });
        }
        
        function displayLogs(logs) {
            const container = document.getElementById('logsContainer');
            
            if (logs.length === 0) {
                container.innerHTML = '<div class="loading">No logs available</div>';
                return;
            }
            
            container.innerHTML = logs.map(log => {
                const timestamp = new Date(log.time || log.StartUTC).toLocaleString();
                const method = log.RequestMethod || 'GET';
                const url = log.RequestPath || log.RequestURI || '/';
                const status = log.DownstreamStatus || log.ResponseCode || 200;
                const duration = log.Duration ? Math.round(log.Duration / 1000000) + 'ms' : 'N/A';
                const clientIP = log.ClientAddr || log.RemoteAddr || 'Unknown';
                
                return `
                    <div class="log-entry">
                        <div class="log-header" onclick="toggleDetails(this)">
                            <span class="method ${method}">${method}</span>
                            <span class="url">${url}</span>
                            <span class="status-${Math.floor(status/100)*100}" style="margin-left: 1rem;">${status}</span>
                            <span class="duration" style="margin-left: 1rem;">${duration}</span>
                            <span class="timestamp" style="float: right;">${timestamp}</span>
                        </div>
                        <div class="log-details">
                            <h4>Request Details</h4>
                            <div class="headers">
                                <div class="header-item"><span class="key">Client IP:</span> <span class="value">${clientIP}</span></div>
                                <div class="header-item"><span class="key">User Agent:</span> <span class="value">${log.RequestUserAgent || 'N/A'}</span></div>
                                <div class="header-item"><span class="key">Referer:</span> <span class="value">${log.RequestReferer || 'N/A'}</span></div>
                                <div class="header-item"><span class="key">Content Type:</span> <span class="value">${log.RequestContentType || 'N/A'}</span></div>
                                <div class="header-item"><span class="key">Content Length:</span> <span class="value">${log.RequestContentLength || 'N/A'}</span></div>
                            </div>
                            
                            <h4>Response Details</h4>
                            <div class="headers">
                                <div class="header-item"><span class="key">Status:</span> <span class="value">${status}</span></div>
                                <div class="header-item"><span class="key">Content Type:</span> <span class="value">${log.DownstreamContentType || 'N/A'}</span></div>
                                <div class="header-item"><span class="key">Content Length:</span> <span class="value">${log.DownstreamContentSize || 'N/A'}</span></div>
                                <div class="header-item"><span class="key">Backend:</span> <span class="value">${log.ServiceName || 'N/A'}</span></div>
                                <div class="header-item"><span class="key">Duration:</span> <span class="value">${duration}</span></div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        function toggleDetails(header) {
            const details = header.nextElementSibling;
            details.style.display = details.style.display === 'block' ? 'none' : 'block';
        }
        
        function clearLogs() {
            document.getElementById('logsContainer').innerHTML = '<div class="loading">Logs cleared</div>';
        }
        
        function toggleAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            if (checkbox.checked) {
                autoRefreshInterval = setInterval(loadLogs, 5000);
            } else {
                clearInterval(autoRefreshInterval);
            }
        }
        
        // Load logs on page load
        loadLogs();
    </script>
</body>
</html>
