server {
    listen 80;
    server_name localhost;
    
    # Enable CORS for log viewer
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
    
    # Main log viewer page
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # Serve log files as JSON
    location /logs/ {
        alias /usr/share/nginx/html/logs/;
        autoindex on;
        autoindex_format json;
        
        # Set content type for log files
        location ~* \.log$ {
            add_header Content-Type application/json;
        }
    }
    
    # API endpoint to get latest logs
    location /api/logs {
        alias /usr/share/nginx/html/logs/;
        try_files /access.log =404;
        add_header Content-Type application/json;
    }
    
    # Health check
    location /health {
        access_log off;
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }
}
