#!/bin/bash

echo "🚀 Starting Eko API with mitmproxy Detailed Logging..."

# Stop existing containers
docker-compose down

# Start with scaling
docker-compose up --scale eko-api=10 --scale eko-api-docs=1 -d

echo ""
echo "✅ Services started!"
echo ""
echo "📊 Access your services:"
echo "   🔍 mitmproxy Web UI:     http://localhost:8205"
echo "   📋 Container Logs:       http://localhost:8204"
echo "   🌐 API Docs:             http://localhost:8201"
echo "   ⚙️  Traefik Dashboard:    http://localhost:8202"
echo ""
echo "🎯 To capture API traffic:"
echo "   1. Use proxy: http://localhost:8203 for your API calls"
echo "   2. View detailed logs at: http://localhost:8205"
echo "   3. Or check logs/detailed_logs.jsonl file"
echo ""
echo "💡 mitmproxy captures FULL request bodies and response data!"
echo "📝 Example: curl -x http://localhost:8203 http://localhost:8201/your-endpoint"
