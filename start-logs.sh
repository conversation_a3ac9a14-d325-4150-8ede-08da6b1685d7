#!/bin/bash

echo "🚀 Starting Eko API with Enhanced Logging..."

# Stop existing containers
docker-compose down

# Start with scaling
docker-compose up --scale eko-api=10 --scale eko-api-docs=1 -d

echo ""
echo "✅ Services started!"
echo ""
echo "📊 Access your logs:"
echo "   🔍 Request/Response Logs: http://localhost:8203"
echo "   📋 Container Logs:        http://localhost:8204"
echo "   🌐 API Docs:             http://localhost:8201"
echo "   ⚙️  Traefik Dashboard:    http://localhost:8202"
echo ""
echo "🔄 Logs will auto-refresh every 5 seconds"
echo "💡 Click on any request to see detailed headers and response info"
