{"ClientAddr":"**********:44678","ClientHost":"**********","ClientPort":"44678","DownstreamContentSize":11,"DownstreamStatus":502,"Duration":4385073,"OriginContentSize":11,"OriginDuration":1620966,"OriginStatus":502,"Overhead":2764107,"RequestAddr":"127.0.0.1:8201","RequestContentSize":0,"RequestCount":1,"RequestHost":"127.0.0.1","RequestMethod":"POST","RequestPath":"/login","RequestPort":"8201","RequestProtocol":"HTTP/1.1","RequestScheme":"http","RetryAttempts":0,"RouterName":"api-docs@docker","ServiceAddr":"**********2:8000","ServiceName":"eko-api-service@docker","ServiceURL":"http://**********2:8000","StartLocal":"2025-06-23T11:40:10.988985615Z","StartUTC":"2025-06-23T11:40:10.988985615Z","downstream_Access-Control-Allow-Credentials":"true","downstream_Access-Control-Allow-Origin":"http://127.0.0.1:8201","downstream_Access-Control-Expose-Headers":"*","downstream_Content-Type":"","downstream_Vary":"Accept-Encoding,Origin","entryPointName":"http-api","level":"info","msg":"","origin_Content-Type":"","origin_Vary":"Accept-Encoding","request_Accept":"application/json, text/plain, */*","request_Accept-Encoding":"gzip, deflate, br, zstd","request_Accept-Language":"en-US,en;q=0.5","request_Content-Length":"76","request_Content-Type":"application/x-www-form-urlencoded","request_Origin":"http://127.0.0.1:8201","request_Priority":"u=0","request_Referer":"http://127.0.0.1:8201/docs","request_Sec-Fetch-Dest":"empty","request_Sec-Fetch-Mode":"cors","request_Sec-Fetch-Site":"same-origin","request_User-Agent":"Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","request_X-Forwarded-Host":"127.0.0.1:8201","request_X-Forwarded-Port":"8201","request_X-Forwarded-Proto":"http","request_X-Forwarded-Server":"cf4029b0e289","request_X-Real-Ip":"**********","request_X-Requested-With":"XMLHttpRequest","time":"2025-06-23T11:40:10Z"}
{"ClientAddr":"**********:44678","ClientHost":"**********","ClientPort":"44678","DownstreamContentSize":11,"DownstreamStatus":502,"Duration":1454088,"OriginContentSize":11,"OriginDuration":1085549,"OriginStatus":502,"Overhead":368539,"RequestAddr":"127.0.0.1:8201","RequestContentSize":0,"RequestCount":2,"RequestHost":"127.0.0.1","RequestMethod":"POST","RequestPath":"/login","RequestPort":"8201","RequestProtocol":"HTTP/1.1","RequestScheme":"http","RetryAttempts":0,"RouterName":"api-docs@docker","ServiceAddr":"**********:8000","ServiceName":"eko-api-service@docker","ServiceURL":"http://**********:8000","StartLocal":"2025-06-23T11:40:13.356338771Z","StartUTC":"2025-06-23T11:40:13.356338771Z","downstream_Access-Control-Allow-Credentials":"true","downstream_Access-Control-Allow-Origin":"http://127.0.0.1:8201","downstream_Access-Control-Expose-Headers":"*","downstream_Content-Type":"","downstream_Vary":"Accept-Encoding,Origin","entryPointName":"http-api","level":"info","msg":"","origin_Content-Type":"","origin_Vary":"Accept-Encoding","request_Accept":"application/json, text/plain, */*","request_Accept-Encoding":"gzip, deflate, br, zstd","request_Accept-Language":"en-US,en;q=0.5","request_Content-Length":"76","request_Content-Type":"application/x-www-form-urlencoded","request_Origin":"http://127.0.0.1:8201","request_Priority":"u=0","request_Referer":"http://127.0.0.1:8201/docs","request_Sec-Fetch-Dest":"empty","request_Sec-Fetch-Mode":"cors","request_Sec-Fetch-Site":"same-origin","request_User-Agent":"Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","request_X-Forwarded-Host":"127.0.0.1:8201","request_X-Forwarded-Port":"8201","request_X-Forwarded-Proto":"http","request_X-Forwarded-Server":"cf4029b0e289","request_X-Real-Ip":"**********","request_X-Requested-With":"XMLHttpRequest","time":"2025-06-23T11:40:13Z"}
{"ClientAddr":"**********:44678","ClientHost":"**********","ClientPort":"44678","DownstreamContentSize":818,"DownstreamStatus":200,"Duration":298512517,"OriginContentSize":818,"OriginDuration":298301019,"OriginStatus":200,"Overhead":211498,"RequestAddr":"127.0.0.1:8201","RequestContentSize":76,"RequestCount":3,"RequestHost":"127.0.0.1","RequestMethod":"POST","RequestPath":"/login","RequestPort":"8201","RequestProtocol":"HTTP/1.1","RequestScheme":"http","RetryAttempts":0,"RouterName":"api-docs@docker","ServiceAddr":"**********:8000","ServiceName":"eko-api-service@docker","ServiceURL":"http://**********:8000","StartLocal":"2025-06-23T11:40:27.820240542Z","StartUTC":"2025-06-23T11:40:27.820240542Z","downstream_Access-Control-Allow-Credentials":"true","downstream_Access-Control-Allow-Origin":"http://127.0.0.1:8201","downstream_Access-Control-Expose-Headers":"*","downstream_Content-Length":"818","downstream_Content-Type":"application/json","downstream_Date":"Mon, 23 Jun 2025 11:40:27 GMT","downstream_Server":"uvicorn","downstream_Vary":"Accept-Encoding,Origin","entryPointName":"http-api","level":"info","msg":"","origin_Access-Control-Allow-Credentials":"true","origin_Access-Control-Allow-Origin":"http://127.0.0.1:8201","origin_Content-Length":"818","origin_Content-Type":"application/json","origin_Date":"Mon, 23 Jun 2025 11:40:27 GMT","origin_Server":"uvicorn","origin_Vary":"Accept-Encoding,Origin","request_Accept":"application/json, text/plain, */*","request_Accept-Encoding":"gzip, deflate, br, zstd","request_Accept-Language":"en-US,en;q=0.5","request_Content-Length":"76","request_Content-Type":"application/x-www-form-urlencoded","request_Origin":"http://127.0.0.1:8201","request_Priority":"u=0","request_Referer":"http://127.0.0.1:8201/docs","request_Sec-Fetch-Dest":"empty","request_Sec-Fetch-Mode":"cors","request_Sec-Fetch-Site":"same-origin","request_User-Agent":"Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","request_X-Forwarded-Host":"127.0.0.1:8201","request_X-Forwarded-Port":"8201","request_X-Forwarded-Proto":"http","request_X-Forwarded-Server":"cf4029b0e289","request_X-Real-Ip":"**********","request_X-Requested-With":"XMLHttpRequest","time":"2025-06-23T11:40:28Z"}
